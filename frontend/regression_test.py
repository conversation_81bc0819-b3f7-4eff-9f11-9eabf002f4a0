"""
回归测试页面模块

提供批量推理与评测功能，支持文件上传、参数配置、并发处理等功能。
"""

import asyncio
import os
from multiprocessing import Process
from pathlib import Path

import aiohttp
import pandas as pd
import streamlit as st

from frontend.base_unit import HOST_PROJECT_DIR
from frontend.output_unit import tail_run_log
from scripts.val_batch_call import load_json_info_for_process, process_batch, save_dict_to_excel
from util.cache_util import (
    CacheManager,
    RegressionTestConfig,
    get_default_regression_config,
    merge_config_with_file_columns
)
from util.common_util import may_have_bad_markdown
from util.date_util import get_cur_time_str
from util.file_util import join_path, is_valid_path, mkdir_if_not_exists
from util.process_util import FilePrintRunner, is_process_running, kill_process_and_children
from util.send_msg_tofeishu import feishu_robot_send_msg, st_gen_url


def _setup_directories(project_dir):
    """创建必要的目录结构"""
    tmp_dir = join_path(project_dir, "tmp")
    inputs_dir = join_path(tmp_dir, "inputs")
    results_dir = join_path(tmp_dir, "results")
    
    mkdir_if_not_exists(tmp_dir)
    mkdir_if_not_exists(inputs_dir)
    mkdir_if_not_exists(results_dir)
    
    return tmp_dir, inputs_dir, results_dir


def _setup_cache_and_config(project_dir):
    """初始化缓存管理器和配置"""
    cache_manager = CacheManager(project_dir)
    cached_config = cache_manager.load_config("regression_test", RegressionTestConfig)
    if cached_config is None:
        cached_config = get_default_regression_config()
    return cache_manager, cached_config


def _render_sidebar_config(cached_config, cache_manager):
    """渲染侧边栏配置"""
    ENV_NAME_DICT = {"本地": "local", "测试": "test", "预发": "preview", "生产": "prod"}
    env_options = ["预发", "本地", "测试"]
    
    default_env_index = 0
    if cached_config.env in env_options:
        default_env_index = env_options.index(cached_config.env)
    
    env = st.selectbox("选择环境", env_options, index=default_env_index, key='env_regression_test')
    env = ENV_NAME_DICT[env]

    # 添加缓存管理按钮
    st.subheader("配置管理")
    if st.button("清除缓存配置", help="清除保存的页面配置，下次将使用默认值"):
        cache_manager.clear_config("regression_test")
        st.success("缓存配置已清除！")
        st.rerun()
    
    return env


def _handle_file_upload_and_display(project_dir, inputs_dir, cached_config):
    """处理文件上传和显示"""
    # 设置默认文件
    default_file_path = join_path(project_dir, 'test/test_data/国际促销员Copilot线下评测集v1.0-by中国区Copilot_2.xlsx')
    st.session_state["uploaded_file_path"] = default_file_path
    st.info(f"默认使用的文件: test/test_data/国际促销员Copilot线下评测集v1.0-by中国区Copilot_2.xlsx")

    # 文件上传
    uploaded_file = st.file_uploader("上传要处理的文件 (Excel 格式)", type=["xlsx"])
    if uploaded_file:
        input_file_path = join_path(inputs_dir, uploaded_file.name)
        with open(input_file_path, "wb") as f:
            f.write(uploaded_file.getbuffer())
        st.session_state["uploaded_file_path"] = input_file_path
        st.success(f"文件已上传: {input_file_path}, 当前正在使用的文件: {st.session_state['uploaded_file_path']}")

    # 读取和显示文件信息
    current_file_path = st.session_state.get("uploaded_file_path")
    available_columns = []
    total_rows = 0

    if current_file_path and os.path.exists(current_file_path):
        df = pd.read_excel(current_file_path)
        available_columns = df.columns.tolist()
        total_rows = len(df)
        st.dataframe(df.iloc[:10], hide_index=True)

        if available_columns:
            st.info(f"文件包含以下列: {', '.join(available_columns)}")
            st.info(f"文件总行数: {total_rows} 行")
            # 根据文件列信息更新缓存配置
            cached_config = merge_config_with_file_columns(cached_config, available_columns)
            # 更新 end_row 默认值
            if cached_config.end_row is None or cached_config.end_row > total_rows:
                cached_config.end_row = total_rows
        else:
            st.warning("无法读取文件列名，请检查文件格式")

    return available_columns, total_rows, cached_config


def _render_row_selection(total_rows, cached_config):
    """渲染数据行选择配置"""
    if total_rows > 0:
        st.subheader("数据行选择")
        col1, col2 = st.columns(2)

        with col1:
            start_row = st.number_input(
                "起始行号 (从1开始)",
                min_value=1,
                max_value=total_rows,
                value=max(1, min(cached_config.start_row, total_rows)),
                help=f"选择要测试的数据起始行号，范围: 1-{total_rows}"
            )

        with col2:
            end_row = st.number_input(
                "结束行号 (包含)",
                min_value=1,
                max_value=total_rows,
                value=max(1, min(cached_config.end_row or total_rows, total_rows)),
                help=f"选择要测试的数据结束行号，范围: 1-{total_rows}"
            )

        # 验证行号范围
        if start_row > end_row:
            st.error("起始行号不能大于结束行号！")
        else:
            selected_rows_count = end_row - start_row + 1
            st.info(f"已选择 {selected_rows_count} 行数据进行测试 (第 {start_row} 行到第 {end_row} 行)")
    else:
        start_row = 1
        end_row = 1

    return start_row, end_row


def _render_column_selection(available_columns, cached_config):
    """渲染列选择配置"""
    if available_columns:
        # 产品名称列选择
        default_product_idx = 0
        if cached_config.product_str in available_columns:
            default_product_idx = available_columns.index(cached_config.product_str)
        elif "SPU" in available_columns:
            default_product_idx = available_columns.index("SPU")
        product_str = st.selectbox("选择产品名称列 (必须提供)",
                                   options=available_columns,
                                   index=default_product_idx,
                                   key="product_column")

        # 问题列选择
        default_query_idx = 0
        if cached_config.query_str in available_columns:
            default_query_idx = available_columns.index(cached_config.query_str)
        else:
            for col in available_columns:
                if "问题" in col and ("印尼" in col or "idn" in col.lower()):
                    default_query_idx = available_columns.index(col)
                    break
        query_str = st.selectbox("选择问题列 (必须提供)",
                                 options=available_columns,
                                 index=default_query_idx,
                                 key="query_column")

        # 参考答案列选择（可选）
        ref_ans_options = ["不选择"] + available_columns
        default_ref_idx = 0
        if cached_config.ref_ans and cached_config.ref_ans in available_columns:
            default_ref_idx = available_columns.index(cached_config.ref_ans) + 1  # +1 因为有"不选择"选项
        else:
            for col in available_columns:
                if ("人工答案" in col and "印尼" in col) or ("参考答案" in col and "印尼" in col) or (
                        "答案" in col and ("idn" in col.lower() or "印尼" in col)):
                    default_ref_idx = available_columns.index(col) + 1  # +1 因为有"不选择"选项
                    break
        ref_ans_selection = st.selectbox("选择参考答案列 (可选，不选择则不进行打分)",
                                         options=ref_ans_options,
                                         index=default_ref_idx,
                                         key="ref_ans_column")
        ref_ans = ref_ans_selection if ref_ans_selection != "不选择" else ""

        # 额外保留列选择（多选）
        used_columns = [product_str, query_str]
        if ref_ans:
            used_columns.append(ref_ans)
        remaining_columns = [col for col in available_columns if col not in used_columns]

        # 使用缓存的额外列选择，但需要过滤掉不存在的列
        cached_extra_columns = [col for col in cached_config.extra_columns_list if col in remaining_columns]
        if not cached_extra_columns:  # 如果缓存中没有有效的额外列，则默认选择所有剩余列
            cached_extra_columns = remaining_columns

        extra_columns_list = st.multiselect("选择希望额外保留的列 (多选)",
                                            options=remaining_columns,
                                            default=cached_extra_columns,
                                            key="extra_columns")
        extra_columns = ",".join(extra_columns_list)
    else:
        # 如果没有可用列，回退到文本输入模式
        st.warning("无法读取文件列名，请手动输入列名")
        product_str = st.text_input("产品名称列名 (product_str)必须提供", value=cached_config.product_str)
        query_str = st.text_input("问题列名 (query_str)必须提供", value=cached_config.query_str or "问题-idn")
        ref_ans = st.text_input("参考答案列名 (ref_ans)非必须，不提供不进行打分", value=cached_config.ref_ans)
        extra_columns = st.text_input("希望额外保留的列（多列以英文逗号,隔开）",
                                      value=",".join(cached_config.extra_columns_list) if cached_config.extra_columns_list else "细分类,问题-zh",
                                      help="例如：列1,列2,列3。这些列的数据将会在输出文件中保留")
        extra_columns_list = extra_columns.split(',') if extra_columns else []

    return product_str, query_str, ref_ans, extra_columns, extra_columns_list


def _render_advanced_config(cached_config):
    """渲染高级配置参数"""
    with st.expander("高级配置"):
        timeout = st.number_input("请求超时时间 (秒)", min_value=10, max_value=300, value=cached_config.timeout)
        max_retries = st.number_input("失败请求重试次数", min_value=0, max_value=5, value=cached_config.max_retries)
        version = st.number_input("参数版本 (version)", min_value=0, max_value=1, value=cached_config.version)
    
    return timeout, max_retries, version


def regression_test(project_dir):
    """回归测试主函数"""
    # 创建主要布局
    sidebar = st.sidebar
    main_content = st.container()

    # 初始化目录和配置
    tmp_dir, inputs_dir, results_dir = _setup_directories(project_dir)
    cache_manager, cached_config = _setup_cache_and_config(project_dir)

    # 渲染侧边栏配置
    with sidebar:
        env = _render_sidebar_config(cached_config, cache_manager)

    with main_content:
        st.title("批量推理与评测工具")

        # 处理文件上传和显示
        available_columns, total_rows, cached_config = _handle_file_upload_and_display(
            project_dir, inputs_dir, cached_config
        )

        # 数据行选择配置
        start_row, end_row = _render_row_selection(total_rows, cached_config)

        # 配置参数
        batch_size = st.number_input("每次并发请求的数量 (batch_size)", min_value=1, max_value=30, value=cached_config.batch_size)
        val_score_flag = st.checkbox("是否评测分数 (val_score_flag)", value=cached_config.val_score_flag)

        # 列选择配置
        column_config = _render_column_selection(available_columns, cached_config)
        product_str, query_str, ref_ans, extra_columns, extra_columns_list = column_config

        # 高级配置参数
        timeout, max_retries, version = _render_advanced_config(cached_config)

        # 文件路径配置
        custom_suffix = st.text_input("自定义文件名后缀 (custom_suffix)", value=cached_config.custom_suffix)
        timestamp = get_cur_time_str()
        cur_input_file_name = os.path.basename(st.session_state["uploaded_file_path"])
        output_file_path = join_path(results_dir, f"{timestamp}_{cur_input_file_name}_F{start_row}T{end_row}_{custom_suffix}_detail.xlsx")
        val_res_file_path = join_path(results_dir, f"{timestamp}_{cur_input_file_name}_F{start_row}T{end_row}_{custom_suffix}_report.xlsx")
        log_path = join_path(tmp_dir, "file_runner.log")

        # 任务控制
        _handle_task_control(
            project_dir, tmp_dir, results_dir, log_path, cache_manager,
            start_row, end_row, batch_size, val_score_flag, product_str, query_str, ref_ans,
            extra_columns, extra_columns_list, timeout, max_retries, version, custom_suffix,
            output_file_path, val_res_file_path, env, total_rows
        )
